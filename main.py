import torch

# Print PyTorch version
print("PyTorch version:", torch.__version__)

# Check if CUDA is available
cuda_available = torch.cuda.is_available()
print("Is CUDA available?", cuda_available)

if cuda_available:
    # Get number of CUDA devices
    device_count = torch.cuda.device_count()
    print("Number of CUDA devices:", device_count)

    # Print the name of each CUDA device
    for i in range(device_count):
        print(f"Device {i} name:", torch.cuda.get_device_name(i))
else:
    print("CUDA is not available. No GPU devices detected.")
